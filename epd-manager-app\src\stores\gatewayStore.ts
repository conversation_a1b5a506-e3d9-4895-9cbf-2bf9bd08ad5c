// EPD Manager App - 網關狀態管理

import { create } from 'zustand';
import { Gateway, GatewayState, ConnectionStatus } from '../types';
import { apiService } from '../services/ApiService';
import { autoPairingService } from '../services/AutoPairingService';
import { webSocketService } from '../services/WebSocketService';
import { CONNECTION_STATUS } from '../utils/constants';

interface GatewayStore extends GatewayState {
  // Actions
  fetchGateways: (storeId: string) => Promise<boolean>;
  selectGateway: (gateway: Gateway) => void;
  clearSelectedGateway: () => void;
  autoPairGateway: (storeId: string, gatewayName?: string) => Promise<boolean>;
  connectToGateway: (gateway: Gateway, storeId: string) => Promise<boolean>;
  disconnectGateway: () => void;
  updateConnectionStatus: (status: ConnectionStatus) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Getters
  getGatewayById: (gatewayId: string) => Gateway | undefined;
  hasSelectedGateway: () => boolean;
  isConnected: () => boolean;
  getCurrentGateway: () => Gateway | null;
}

export const useGatewayStore = create<GatewayStore>()((set, get) => ({
  // Initial state
  gateways: [],
  selectedGateway: null,
  connectionStatus: CONNECTION_STATUS.DISCONNECTED,
  loading: false,
  error: null,

  // Actions
  fetchGateways: async (storeId: string) => {
    set({ loading: true, error: null });

    try {
      const result = await apiService.getGateways(storeId);

      if (result.success && result.data) {
        set({
          gateways: result.data,
          loading: false,
          error: null
        });
        return true;
      } else {
        set({
          loading: false,
          error: result.error || '獲取網關列表失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '獲取網關列表失敗'
      });
      return false;
    }
  },

  selectGateway: (gateway: Gateway) => {
    set({ selectedGateway: gateway });
    console.log('選擇網關:', gateway.name);
  },

  clearSelectedGateway: () => {
    set({ selectedGateway: null });
    console.log('清除選擇的網關');
  },

  autoPairGateway: async (storeId: string, gatewayName?: string) => {
    set({ loading: true, error: null });

    try {
      const result = await autoPairingService.autoCreateAndConnectGateway(storeId, gatewayName);

      if (result.success && result.gateway) {
        // 更新網關列表
        const { gateways } = get();
        const updatedGateways = [...gateways, result.gateway];
        
        set({
          gateways: updatedGateways,
          selectedGateway: result.gateway,
          connectionStatus: CONNECTION_STATUS.CONNECTED,
          loading: false,
          error: null
        });

        console.log('自動配對成功:', result.gateway.name);
        return true;
      } else {
        set({
          loading: false,
          error: result.error || '自動配對失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '自動配對失敗'
      });
      return false;
    }
  },

  connectToGateway: async (gateway: Gateway, storeId: string) => {
    set({ loading: true, error: null, connectionStatus: CONNECTION_STATUS.CONNECTING });

    try {
      const result = await autoPairingService.reconnectToGateway(gateway, storeId);

      if (result.success && result.gateway) {
        set({
          selectedGateway: result.gateway,
          connectionStatus: CONNECTION_STATUS.CONNECTED,
          loading: false,
          error: null
        });

        console.log('連接網關成功:', result.gateway.name);
        return true;
      } else {
        set({
          connectionStatus: CONNECTION_STATUS.ERROR,
          loading: false,
          error: result.error || '連接網關失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        connectionStatus: CONNECTION_STATUS.ERROR,
        loading: false,
        error: error.message || '連接網關失敗'
      });
      return false;
    }
  },

  disconnectGateway: () => {
    try {
      autoPairingService.disconnectCurrentGateway();
      set({
        connectionStatus: CONNECTION_STATUS.DISCONNECTED,
        selectedGateway: null
      });
      console.log('網關已斷開連接');
    } catch (error: any) {
      console.error('斷開網關連接失敗:', error);
      set({ error: error.message });
    }
  },

  updateConnectionStatus: (status: ConnectionStatus) => {
    set({ connectionStatus: status });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // Getters
  getGatewayById: (gatewayId: string) => {
    const { gateways } = get();
    return gateways.find(gateway => gateway._id === gatewayId);
  },

  hasSelectedGateway: () => {
    const { selectedGateway } = get();
    return !!selectedGateway;
  },

  isConnected: () => {
    const { connectionStatus } = get();
    return connectionStatus === CONNECTION_STATUS.CONNECTED;
  },

  getCurrentGateway: () => {
    const { selectedGateway } = get();
    return selectedGateway;
  }
}));

// 設置 WebSocket 狀態變化監聽器
webSocketService.addStatusChangeHandler((status: ConnectionStatus) => {
  useGatewayStore.getState().updateConnectionStatus(status);
});

// 導出便捷的 hooks
export const useGateways = () => {
  const store = useGatewayStore();
  return {
    // State
    gateways: store.gateways,
    selectedGateway: store.selectedGateway,
    connectionStatus: store.connectionStatus,
    loading: store.loading,
    error: store.error,
    
    // Actions
    fetchGateways: store.fetchGateways,
    selectGateway: store.selectGateway,
    clearSelectedGateway: store.clearSelectedGateway,
    autoPairGateway: store.autoPairGateway,
    connectToGateway: store.connectToGateway,
    disconnectGateway: store.disconnectGateway,
    clearError: store.clearError,
    
    // Getters
    getGatewayById: store.getGatewayById,
    hasSelectedGateway: store.hasSelectedGateway(),
    isConnected: store.isConnected(),
    getCurrentGateway: store.getCurrentGateway(),
  };
};

export const useGatewayActions = () => {
  const store = useGatewayStore();
  return {
    fetchGateways: store.fetchGateways,
    selectGateway: store.selectGateway,
    clearSelectedGateway: store.clearSelectedGateway,
    autoPairGateway: store.autoPairGateway,
    connectToGateway: store.connectToGateway,
    disconnectGateway: store.disconnectGateway,
    updateConnectionStatus: store.updateConnectionStatus,
    clearError: store.clearError,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};

export const useGatewayState = () => {
  const store = useGatewayStore();
  return {
    gateways: store.gateways,
    selectedGateway: store.selectedGateway,
    connectionStatus: store.connectionStatus,
    loading: store.loading,
    error: store.error,
    hasSelectedGateway: store.hasSelectedGateway(),
    isConnected: store.isConnected(),
    getCurrentGateway: store.getCurrentGateway(),
  };
};
