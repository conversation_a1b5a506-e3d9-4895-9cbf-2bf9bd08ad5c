// EPD Manager App - 生成器工具函數

/**
 * 生成隨機 MAC 地址
 * @returns 格式為 AA:BB:CC:DD:EE:FF 的 MAC 地址
 */
export function generateRandomMac(): string {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  
  return mac;
}

/**
 * 生成隨機 IP 地址（192.168.x.x 範圍）
 * @returns 格式為 192.168.x.x 的 IP 地址
 */
export function generateRandomIp(): string {
  const subnet = Math.floor(Math.random() * 256);
  const host = Math.floor(Math.random() * 256);
  return `192.168.${subnet}.${host}`;
}

/**
 * 生成隨機網關名稱
 * @param prefix 名稱前綴，默認為 "新網關"
 * @returns 格式為 "新網關-XXXX" 的名稱
 */
export function generateGatewayName(prefix: string = "新網關"): string {
  const randomNumber = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}-${randomNumber}`;
}

/**
 * 生成隨機設備 imageCode
 * @returns 8位數字的 imageCode
 */
export function generateImageCode(): string {
  return Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
}

/**
 * 生成隨機電池電量
 * @returns 0-100 之間的整數
 */
export function generateBatteryLevel(): number {
  return Math.floor(Math.random() * 101);
}

/**
 * 生成隨機信號強度
 * @returns -100 到 0 之間的整數
 */
export function generateRSSI(): number {
  return -1 * Math.floor(Math.random() * 101);
}

/**
 * 生成隨機設備尺寸
 * @returns 常見的電子紙顯示器尺寸
 */
export function generateDeviceSize(): string {
  const sizes = ['1.54"', '2.13"', '2.9"', '3.7"', '4.2"', '5.83"', '6"', '7.5"', '10.3"'];
  return sizes[Math.floor(Math.random() * sizes.length)];
}

/**
 * 生成隨機顏色類型
 * @returns 電子紙顯示器支持的顏色類型
 */
export function generateColorType(): 'BW' | 'BWR' | 'BWRY' {
  const types: ('BW' | 'BWR' | 'BWRY')[] = ['BW', 'BWR', 'BWRY'];
  return types[Math.floor(Math.random() * types.length)];
}

/**
 * 生成唯一 ID
 * @returns 基於時間戳和隨機數的唯一 ID
 */
export function generateUniqueId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成隨機固件版本
 * @returns 格式為 x.y.z 的版本號
 */
export function generateFirmwareVersion(): string {
  const major = Math.floor(Math.random() * 3) + 1; // 1-3
  const minor = Math.floor(Math.random() * 10);    // 0-9
  const patch = Math.floor(Math.random() * 10);    // 0-9
  return `${major}.${minor}.${patch}`;
}

/**
 * 生成隨機網關模型
 * @returns 網關模型名稱
 */
export function generateGatewayModel(): string {
  const models = ['GW-1000', 'GW-2000', 'GW-3000', 'EPD-Gateway-Pro'];
  return models[Math.floor(Math.random() * models.length)];
}

/**
 * 驗證 MAC 地址格式
 * @param mac MAC 地址字符串
 * @returns 是否為有效的 MAC 地址格式
 */
export function isValidMacAddress(mac: string): boolean {
  const macRegex = /^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(mac);
}

/**
 * 驗證 IP 地址格式
 * @param ip IP 地址字符串
 * @returns 是否為有效的 IP 地址格式
 */
export function isValidIpAddress(ip: string): boolean {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
}

/**
 * 格式化 MAC 地址（統一大寫格式）
 * @param mac MAC 地址字符串
 * @returns 格式化後的 MAC 地址
 */
export function formatMacAddress(mac: string): string {
  return mac.toUpperCase();
}

/**
 * 生成測試用的預設設備列表
 * @param count 設備數量，默認為 3
 * @returns 預設設備列表
 */
export function generateDefaultDevices(count: number = 3): any[] {
  const devices = [];
  const colorTypes: ('BW' | 'BWR' | 'BWRY')[] = ['BWR', 'BW', 'BWRY'];
  
  for (let i = 0; i < count; i++) {
    devices.push({
      macAddress: generateRandomMac(),
      status: 'online',
      data: {
        size: generateDeviceSize(),
        battery: generateBatteryLevel(),
        rssi: generateRSSI(),
        colorType: colorTypes[i % colorTypes.length],
        imageCode: generateImageCode()
      }
    });
  }
  
  return devices;
}
