# EPD Manager App - Android 構建指南

## 📱 Android 應用構建說明

由於依賴版本衝突，我們提供多種構建方案。

## 🚀 方案一：使用 Expo Go 測試（推薦）

這是最簡單的測試方法：

### 1. 安裝 Expo Go
在 Android 設備上安裝 Expo Go 應用：
- [Google Play Store](https://play.google.com/store/apps/details?id=host.exp.exponent)

### 2. 啟動開發服務器
```bash
npm start
```

### 3. 掃描 QR 碼
使用 Expo Go 掃描終端中顯示的 QR 碼即可運行應用。

## 🔧 方案二：構建 APK（需要 Expo 帳號）

### 1. 創建 Expo 帳號
訪問 [https://expo.dev/](https://expo.dev/) 創建免費帳號。

### 2. 登入 EAS CLI
```bash
npx eas login
```

### 3. 配置項目
```bash
npx eas build:configure
```

### 4. 構建 APK
```bash
npx eas build --platform android --profile preview
```

### 5. 下載 APK
構建完成後，在 Expo 控制台下載 APK 文件。

## 🏠 方案三：本地構建（需要 Android Studio）

### 前置條件
1. 安裝 Android Studio
2. 配置 Android SDK
3. 設置環境變量 ANDROID_HOME

### 構建步驟
```bash
# 1. 生成原生代碼
npx expo prebuild

# 2. 構建 APK
cd android
./gradlew assembleDebug

# 3. APK 位置
# android/app/build/outputs/apk/debug/app-debug.apk
```

## 📋 當前應用功能

### ✅ 已實現功能
- 用戶登入認證
- 門店選擇
- 一鍵網關配對
- WebSocket 連接模擬
- 連接狀態監控
- 設備管理

### 🎯 核心特色
- **自動化配對**：消除手動複製 MAC 地址和 WebSocket 配置
- **一鍵操作**：整個配對流程簡化為一個按鈕
- **實時監控**：WebSocket 連接狀態和網關信息實時顯示
- **完整模擬**：實現 test-ws-client-interactive.js 的所有功能

## 🔧 故障排除

### 依賴衝突問題
如果遇到依賴衝突，可以嘗試：

```bash
# 清除 node_modules 和重新安裝
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# 或者使用 yarn
yarn install
```

### Metro 緩存問題
```bash
npx expo start --clear
```

### Android 構建問題
```bash
# 清除 Android 構建緩存
cd android
./gradlew clean

# 重新構建
./gradlew assembleDebug
```

## 📱 測試建議

### 1. 功能測試
- 登入功能
- 門店選擇
- 一鍵配對
- WebSocket 連接
- 設備管理

### 2. 網絡測試
- 不同網絡環境下的連接穩定性
- 網絡中斷後的自動重連
- 服務器地址配置

### 3. 用戶體驗測試
- 界面響應速度
- 錯誤提示清晰度
- 操作流程順暢性

## 🚀 部署建議

### 開發階段
使用 Expo Go 進行快速測試和調試。

### 測試階段
構建 APK 進行完整功能測試。

### 生產階段
使用 `production` 配置構建最終版本：
```bash
npx eas build --platform android --profile production
```

## 📞 技術支持

如果遇到構建問題，可以：

1. 檢查 [Expo 文檔](https://docs.expo.dev/)
2. 查看 [React Native 文檔](https://reactnative.dev/)
3. 參考 [EAS Build 文檔](https://docs.expo.dev/build/introduction/)

## 🎉 總結

EPD Manager App 已成功實現核心的自動化配對功能，可以通過多種方式進行構建和測試。推薦先使用 Expo Go 進行功能驗證，然後根據需要構建 APK 進行完整測試。
