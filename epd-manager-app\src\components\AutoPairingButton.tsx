// EPD Manager App - 一鍵配對按鈕組件

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useGateways } from '../stores/gatewayStore';
import { useStores } from '../stores/storeStore';
import { COLORS, SIZES, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

interface AutoPairingButtonProps {
  onSuccess?: (gateway: any) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  style?: any;
}

export const AutoPairingButton: React.FC<AutoPairingButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  style
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { selectedStore } = useStores();
  const { autoPairGateway, loading, error } = useGateways();

  const handleAutoPairing = async () => {
    // 檢查是否選擇了門店
    if (!selectedStore) {
      Alert.alert('錯誤', '請先選擇門店');
      return;
    }

    // 確認對話框
    Alert.alert(
      '確認配對',
      `確定要為門店「${selectedStore.name}」創建新網關嗎？`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '確定',
          onPress: performAutoPairing,
        },
      ]
    );
  };

  const performAutoPairing = async () => {
    if (!selectedStore) return;

    setIsProcessing(true);

    try {
      console.log('開始自動配對流程...');
      
      const success = await autoPairGateway(selectedStore._id || selectedStore.id);

      if (success) {
        Alert.alert(
          '配對成功',
          SUCCESS_MESSAGES.AUTO_PAIRING_SUCCESS,
          [
            {
              text: '確定',
              onPress: () => {
                if (onSuccess) {
                  onSuccess(selectedStore);
                }
              },
            },
          ]
        );
      } else {
        const errorMessage = error || ERROR_MESSAGES.AUTO_PAIRING_FAILED;
        Alert.alert('配對失敗', errorMessage);
        
        if (onError) {
          onError(errorMessage);
        }
      }
    } catch (err: any) {
      const errorMessage = err.message || ERROR_MESSAGES.AUTO_PAIRING_FAILED;
      Alert.alert('配對失敗', errorMessage);
      
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const isButtonDisabled = disabled || isProcessing || loading || !selectedStore;

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.button,
          isButtonDisabled && styles.buttonDisabled
        ]}
        onPress={handleAutoPairing}
        disabled={isButtonDisabled}
        activeOpacity={0.8}
      >
        {(isProcessing || loading) ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator 
              size="small" 
              color={COLORS.SURFACE} 
              style={styles.loadingIndicator}
            />
            <Text style={styles.buttonTextLoading}>
              配對中...
            </Text>
          </View>
        ) : (
          <Text style={[
            styles.buttonText,
            isButtonDisabled && styles.buttonTextDisabled
          ]}>
            🚀 一鍵配對新網關
          </Text>
        )}
      </TouchableOpacity>
      
      {!selectedStore && (
        <Text style={styles.hintText}>
          請先選擇門店
        </Text>
      )}
      
      {selectedStore && (
        <Text style={styles.infoText}>
          將為門店「{selectedStore.name}」創建新網關
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: SIZES.SPACING_MD,
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SIZES.SPACING_XL,
    paddingVertical: SIZES.SPACING_LG,
    borderRadius: SIZES.BORDER_RADIUS_LG,
    minWidth: 200,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: COLORS.TEXT_DISABLED,
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonTextDisabled: {
    color: COLORS.TEXT_SECONDARY,
  },
  buttonTextLoading: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingIndicator: {
    marginRight: SIZES.SPACING_SM,
  },
  hintText: {
    color: COLORS.WARNING,
    fontSize: SIZES.FONT_SIZE_SM,
    marginTop: SIZES.SPACING_SM,
    textAlign: 'center',
  },
  infoText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_SIZE_SM,
    marginTop: SIZES.SPACING_SM,
    textAlign: 'center',
    paddingHorizontal: SIZES.SPACING_MD,
  },
});

export default AutoPairingButton;
